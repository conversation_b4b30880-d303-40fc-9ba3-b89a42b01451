import { Injectable } from '@angular/core';
import { catchError, from, map, Observable, throwError } from 'rxjs';
import { SupabaseService } from '../../../core';
import {
  FacebookApp,
  FacebookAppFilters,
  FacebookAppResponse,
  FacebookLoginResponse,
  FacebookPage,
  FacebookToken,
  FacebookUser,
  FacebookUserInfo,
  FacebookAdAccount,
  FacebookUserWithAdAccounts,
  FacebookAccountFromAPI,
  FacebookAccountConflict,
  FacebookAccountSelectionData,
} from '../models';

declare global {
  interface Window {
    FB: any;
    fbAsyncInit: () => void;
  }
}

@Injectable({
  providedIn: 'root',
})
export class FacebookAppService {
  private isSDKLoaded = false;
  private currentAppId: string | null = null;

  constructor(private supabaseService: SupabaseService) {}

  // Facebook Apps CRUD Operations
  getFacebookApps(
    filters: FacebookAppFilters = {},
  ): Observable<FacebookAppResponse> {
    let query = this.supabaseService.client
      .from('fbam_facebook_apps')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active);
    }

    if (filters.search) {
      query = query.or(
        `name.ilike.%${filters.search}%,app_id.ilike.%${filters.search}%`,
      );
    }

    return from(query).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching Facebook apps:', response.error);
          throw response.error;
        }

        return {
          data: response.data || [],
          total_count: response.count || 0,
        };
      }),
      catchError((error) => {
        console.error('Error fetching Facebook apps:', error);
        return throwError(() => error);
      }),
    );
  }

  createFacebookApp(app: Omit<FacebookApp, 'id'>): Observable<FacebookApp> {
    return from(
      this.supabaseService.client
        .from('fbam_facebook_apps')
        .insert([app])
        .select()
        .single(),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error creating Facebook app:', response.error);
          throw response.error;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error creating Facebook app:', error);
        return throwError(() => error);
      }),
    );
  }

  updateFacebookApp(
    id: string,
    updates: Partial<FacebookApp>,
  ): Observable<FacebookApp> {
    return from(
      this.supabaseService.client
        .from('fbam_facebook_apps')
        .update(updates)
        .eq('id', id)
        .select()
        .single(),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error updating Facebook app:', response.error);
          throw response.error;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error updating Facebook app:', error);
        return throwError(() => error);
      }),
    );
  }

  deleteFacebookApp(id: string): Observable<void> {
    return from(
      this.supabaseService.client
        .from('fbam_facebook_apps')
        .delete()
        .eq('id', id),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error deleting Facebook app:', response.error);
          throw response.error;
        }
      }),
      catchError((error) => {
        console.error('Error deleting Facebook app:', error);
        return throwError(() => error);
      }),
    );
  }

  // Facebook User Operations
  getAllUsers(): Observable<FacebookUser[]> {
    return from(
      this.supabaseService.client
        .from('fbam_facebook_users')
        .select(
          `
          id,
          app_id,
          facebook_user_id,
          name,
          email,
          profile_picture_url,
          is_long_lived,
          permissions,
          expires_at,
          last_login,
          created_at,
          updated_at
        `,
        )
        .order('last_login', { ascending: false }),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching all users:', response.error);
          throw response.error;
        }
        return response.data || [];
      }),
      catchError((error) => {
        console.error('Error fetching all users:', error);
        return throwError(() => error);
      }),
    );
  }

  // Keep getAllTokens for backward compatibility
  getAllTokens(): Observable<FacebookToken[]> {
    return this.getAllUsers();
  }

  getUsersForApp(appId: string): Observable<FacebookUser[]> {
    return from(
      this.supabaseService.client
        .from('fbam_facebook_users')
        .select(
          `
          id,
          app_id,
          facebook_user_id,
          name,
          email,
          profile_picture_url,
          is_long_lived,
          permissions,
          expires_at,
          last_login,
          created_at,
          updated_at
        `,
        )
        .eq('app_id', appId)
        .order('last_login', { ascending: false }),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching users:', response.error);
          throw response.error;
        }
        return response.data || [];
      }),
      catchError((error) => {
        console.error('Error fetching users:', error);
        return throwError(() => error);
      }),
    );
  }

  // Keep getTokensForApp for backward compatibility
  getTokensForApp(appId: string): Observable<FacebookToken[]> {
    return this.getUsersForApp(appId);
  }

  saveUser(user: Omit<FacebookUser, 'id'>): Observable<FacebookUser> {
    return from(
      this.supabaseService.client
        .from('fbam_facebook_users')
        .upsert([user], {
          onConflict: 'app_id,facebook_user_id',
          ignoreDuplicates: false,
        })
        .select(
          `
          id,
          app_id,
          facebook_user_id,
          name,
          email,
          profile_picture_url,
          is_long_lived,
          permissions,
          expires_at,
          last_login,
          created_at,
          updated_at
        `,
        )
        .single(),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error saving user:', response.error);
          throw response.error;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error saving user:', error);
        return throwError(() => error);
      }),
    );
  }

  // Keep saveToken for backward compatibility
  saveToken(token: Omit<FacebookToken, 'id'>): Observable<FacebookToken> {
    return this.saveUser(token as Omit<FacebookUser, 'id'>);
  }

  deleteUser(id: string): Observable<void> {
    return from(
      this.supabaseService.client
        .from('fbam_facebook_users')
        .delete()
        .eq('id', id),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error deleting user:', response.error);
          throw response.error;
        }
      }),
      catchError((error) => {
        console.error('Error deleting user:', error);
        return throwError(() => error);
      }),
    );
  }

  // Keep deleteToken for backward compatibility
  deleteToken(id: string): Observable<void> {
    return this.deleteUser(id);
  }

  // Facebook SDK Integration
  async initializeFacebookSDK(appId: string): Promise<void> {
    if (this.isSDKLoaded && this.currentAppId === appId) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      try {
        // Load Facebook SDK script if not already loaded
        if (!document.getElementById('facebook-jssdk')) {
          const script = document.createElement('script');
          script.id = 'facebook-jssdk';
          script.src = 'https://connect.facebook.net/en_US/sdk.js';
          document.head.appendChild(script);
        }

        window.fbAsyncInit = () => {
          window.FB.init({
            appId: appId,
            cookie: true,
            xfbml: true,
            version: 'v22.0',
          });

          this.isSDKLoaded = true;
          this.currentAppId = appId;
          resolve();
        };

        // If FB is already loaded, initialize immediately
        if (window.FB) {
          window.fbAsyncInit();
        }
      } catch (error) {
        console.error('Error initializing Facebook SDK:', error);
        reject(error);
      }
    });
  }

  async loginWithFacebook(): Promise<FacebookLoginResponse> {
    return new Promise((resolve, reject) => {
      if (!window.FB) {
        reject(new Error('Facebook SDK not loaded'));
        return;
      }

      window.FB.login(
        (response: any) => {
          if (response.authResponse) {
            resolve(response.authResponse);
          } else {
            reject(new Error('Facebook login failed or was cancelled'));
          }
        },
        {
          scope:
            'ads_read,business_management,pages_show_list,ads_management,pages_read_engagement,pages_manage_ads,pages_read_user_content,pages_manage_metadata,pages_manage_posts',
        },
      );
    });
  }

  async getUserInfo(accessToken: string): Promise<FacebookUserInfo> {
    return new Promise((resolve, reject) => {
      if (!window.FB) {
        reject(new Error('Facebook SDK not loaded'));
        return;
      }

      window.FB.api(
        '/me',
        { fields: 'id,name,email,picture', access_token: accessToken },
        (response: any) => {
          if (response && !response.error) {
            resolve(response);
          } else {
            reject(
              new Error(response.error?.message || 'Failed to get user info'),
            );
          }
        },
      );
    });
  }

  async getUserPages(accessToken: string): Promise<FacebookPage[]> {
    return new Promise((resolve, reject) => {
      if (!window.FB) {
        reject(new Error('Facebook SDK not loaded'));
        return;
      }

      window.FB.api(
        '/me/accounts',
        { access_token: accessToken },
        (response: any) => {
          if (response && response.data && !response.error) {
            resolve(response.data);
          } else {
            reject(
              new Error(response.error?.message || 'Failed to get user pages'),
            );
          }
        },
      );
    });
  }

  async extendAccessToken(
    shortLivedToken: string,
    appId: string,
    appSecret: string,
  ): Promise<string> {
    try {
      const response = await fetch(
        `https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token&client_id=${appId}&client_secret=${appSecret}&fb_exchange_token=${shortLivedToken}`,
      );

      const data = await response.json();

      if (data.access_token) {
        return data.access_token;
      } else {
        throw new Error(data.error?.message || 'Failed to extend access token');
      }
    } catch (error) {
      console.error('Error extending access token:', error);
      throw error;
    }
  }

  logout(): void {
    if (window.FB) {
      window.FB.logout();
    }
  }

  /**
   * Check for account conflicts when adding new accounts
   */
  checkAccountConflicts(accounts: FacebookAccountFromAPI[]): Observable<FacebookAccountSelectionData> {
    const accountIds = accounts.map(acc => acc.id);

    return from(
      this.supabaseService.client
        .from('fbam_facebook_ad_accounts')
        .select(`
          ad_account_id,
          user:fbam_facebook_users(id, name, email)
        `)
        .in('ad_account_id', accountIds)
        .eq('is_accessible', true)
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error checking account conflicts:', response.error);
          throw response.error;
        }

        const existingAccounts = response.data || [];
        const conflicted_accounts: FacebookAccountConflict[] = [];
        const available_accounts: FacebookAccountFromAPI[] = [];

        accounts.forEach(account => {
          const existing = existingAccounts.find(ea => ea.ad_account_id === account.id);
          if (existing && existing.user) {
            conflicted_accounts.push({
              account,
              existing_user: {
                id: existing.user.id,
                name: existing.user.name,
                email: existing.user.email
              }
            });
          } else {
            available_accounts.push(account);
          }
        });

        return {
          available_accounts,
          conflicted_accounts,
          user_info: {
            id: '',
            name: '',
            email: ''
          }
        } as FacebookAccountSelectionData;
      }),
      catchError((error) => {
        console.error('Error checking account conflicts:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Save selected accounts for a user
   */
  saveSelectedAccounts(userId: string, selectedAccountIds: string[], allAccounts: FacebookAccountFromAPI[]): Observable<FacebookAdAccount[]> {
    const accountsToSave = allAccounts
      .filter(acc => selectedAccountIds.includes(acc.id))
      .map(acc => ({
        user_id: userId,
        ad_account_id: acc.id,
        ad_account_name: acc.name,
        account_status: acc.account_status,
        currency: acc.currency,
        timezone_name: acc.timezone_name,
        business_id: acc.business_id,
        business_name: acc.business_name,
        permissions: [],
        is_accessible: true,
        last_synced: new Date().toISOString(),
      }));

    return from(
      this.supabaseService.client
        .from('fbam_facebook_ad_accounts')
        .upsert(accountsToSave, {
          onConflict: 'user_id,ad_account_id',
          ignoreDuplicates: false,
        })
        .select('*')
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error saving selected accounts:', response.error);
          throw response.error;
        }
        return response.data || [];
      }),
      catchError((error) => {
        console.error('Error saving selected accounts:', error);
        return throwError(() => error);
      }),
    );
  }
}
