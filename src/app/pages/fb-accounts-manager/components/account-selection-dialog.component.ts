import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { CheckboxModule } from 'primeng/checkbox';
import { TagModule } from 'primeng/tag';
import { DividerModule } from 'primeng/divider';
import {
  FacebookAccountSelectionData,
  FacebookAccountSelectionResult,
  FacebookAccountFromAPI,
  FacebookAccountConflict
} from '../models';
import { getAccountStatusLabel, getAccountStatusSeverity } from '../../../core/types/facebook-accounts-structure.types';

@Component({
  selector: 'app-account-selection-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    DialogModule,
    CheckboxModule,
    TagModule,
    DividerModule,
  ],
  template: `
    <p-dialog
      [visible]="visible"
      [modal]="true"
      [closable]="false"
      [draggable]="false"
      [resizable]="false"
      styleClass="account-selection-dialog"
      header="Select Facebook Ad Accounts">

      <div class="dialog-content">
        <!-- User Info -->
        <div class="user-info">
          <div class="user-avatar">
            <i class="fab fa-facebook-f"></i>
          </div>
          <div class="user-details">
            <h3>{{ selectionData?.user_info?.name }}</h3>
            <p>{{ selectionData?.user_info?.email }}</p>
          </div>
        </div>

        <p-divider></p-divider>

        <!-- Available Accounts -->
        <div *ngIf="selectionData?.available_accounts?.length" class="accounts-section">
          <h4>
            <i class="pi pi-check-circle"></i>
            Available Accounts ({{ selectionData?.available_accounts?.length }})
          </h4>
          <p class="section-description">Select which accounts you want to add:</p>

          <div class="accounts-list">
            <div
              *ngFor="let account of selectionData?.available_accounts"
              class="account-item available">
              <p-checkbox
                [binary]="true"
                [(ngModel)]="selectedAccounts[account.id]"
                [inputId]="'account-' + account.id">
              </p-checkbox>
              <label [for]="'account-' + account.id" class="account-label">
                <div class="account-info">
                  <div class="account-name">{{ account.name }}</div>
                  <div class="account-meta">
                    <span class="account-id">{{ account.id }}</span>
                    <p-tag
                      [value]="getAccountStatusLabel(account.account_status)"
                      [severity]="getAccountStatusSeverity(account.account_status)">
                    </p-tag>
                    <span class="account-currency">{{ account.currency }}</span>
                  </div>
                </div>
              </label>
            </div>
          </div>
        </div>

        <!-- Conflicted Accounts -->
        <div *ngIf="selectionData?.conflicted_accounts?.length" class="accounts-section">
          <h4>
            <i class="pi pi-exclamation-triangle"></i>
            Conflicted Accounts ({{ selectionData?.conflicted_accounts?.length }})
          </h4>
          <p class="section-description">These accounts are already assigned to other users:</p>

          <div class="accounts-list">
            <div
              *ngFor="let conflict of selectionData?.conflicted_accounts"
              class="account-item conflicted">
              <div class="conflict-icon">
                <i class="pi pi-lock"></i>
              </div>
              <div class="account-info">
                <div class="account-name">{{ conflict.account.name }}</div>
                <div class="account-meta">
                  <span class="account-id">{{ conflict.account.id }}</span>
                  <p-tag
                    [value]="getAccountStatusLabel(conflict.account.account_status)"
                    [severity]="getAccountStatusSeverity(conflict.account.account_status)">
                  </p-tag>
                  <span class="account-currency">{{ conflict.account.currency }}</span>
                </div>
                <div class="conflict-info">
                  <i class="pi pi-user"></i>
                  Already assigned to: <strong>{{ conflict.existing_user.name }}</strong>
                  <span *ngIf="conflict.existing_user.email">({{ conflict.existing_user.email }})</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- No Accounts Message -->
        <div *ngIf="!selectionData?.available_accounts?.length && !selectionData?.conflicted_accounts?.length"
             class="no-accounts">
          <i class="pi pi-info-circle"></i>
          <h4>No Ad Accounts Found</h4>
          <p>This Facebook user doesn't have access to any ad accounts, or all accounts are already assigned.</p>
        </div>
      </div>

      <ng-template pTemplate="footer">
        <div class="dialog-footer">
          <p-button
            label="Cancel"
            [outlined]="true"
            severity="secondary"
            (onClick)="onCancel()">
          </p-button>
          <p-button
            label="Add Selected Accounts"
            severity="primary"
            [disabled]="!hasSelectedAccounts()"
            (onClick)="onConfirm()">
          </p-button>
        </div>
      </ng-template>
    </p-dialog>
  `,
  styles: [`
    .account-selection-dialog {
      width: 90vw;
      max-width: 600px;
    }

    .dialog-content {
      padding: 0;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .user-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: linear-gradient(135deg, #5521be, #e036af);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.2rem;
    }

    .user-details h3 {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .user-details p {
      margin: 0.25rem 0 0 0;
      color: var(--text-color-secondary);
      font-size: 0.9rem;
    }

    .accounts-section {
      margin-bottom: 1.5rem;
    }

    .accounts-section h4 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin: 0 0 0.5rem 0;
      font-size: 1rem;
      font-weight: 600;
    }

    .section-description {
      margin: 0 0 1rem 0;
      color: var(--text-color-secondary);
      font-size: 0.9rem;
    }

    .accounts-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .account-item {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
      padding: 1rem;
      border: 1px solid var(--surface-border);
      border-radius: 8px;
      transition: all 0.2s ease;
    }

    .account-item.available {
      background: var(--surface-ground);
    }

    .account-item.available:hover {
      background: var(--surface-hover);
      border-color: var(--primary-color);
    }

    .account-item.conflicted {
      background: var(--red-50);
      border-color: var(--red-200);
    }

    .account-label {
      flex: 1;
      cursor: pointer;
    }

    .account-info {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .account-name {
      font-weight: 600;
      font-size: 0.95rem;
    }

    .account-meta {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      flex-wrap: wrap;
    }

    .account-id {
      font-family: monospace;
      font-size: 0.8rem;
      color: var(--text-color-secondary);
    }

    .account-currency {
      font-size: 0.8rem;
      color: var(--text-color-secondary);
    }

    .conflict-icon {
      color: var(--red-500);
      font-size: 1.1rem;
      margin-top: 0.1rem;
    }

    .conflict-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.85rem;
      color: var(--red-600);
      margin-top: 0.25rem;
    }

    .no-accounts {
      text-align: center;
      padding: 2rem;
      color: var(--text-color-secondary);
    }

    .no-accounts i {
      font-size: 2rem;
      margin-bottom: 1rem;
      color: var(--primary-color);
    }

    .no-accounts h4 {
      margin: 0 0 0.5rem 0;
    }

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 0.75rem;
    }
  `]
})
export class AccountSelectionDialogComponent {
  @Input() visible = false;
  @Input() selectionData: FacebookAccountSelectionData | null = null;
  @Output() result = new EventEmitter<FacebookAccountSelectionResult>();

  selectedAccounts: { [accountId: string]: boolean } = {};

  getAccountStatusLabel = getAccountStatusLabel;
  getAccountStatusSeverity = getAccountStatusSeverity;

  hasSelectedAccounts(): boolean {
    return Object.values(this.selectedAccounts).some(selected => selected);
  }

  onConfirm(): void {
    const selectedAccountIds = Object.keys(this.selectedAccounts)
      .filter(accountId => this.selectedAccounts[accountId]);

    this.result.emit({
      selected_accounts: selectedAccountIds,
      confirmed: true
    });

    this.resetSelection();
  }

  onCancel(): void {
    this.result.emit({
      selected_accounts: [],
      confirmed: false
    });

    this.resetSelection();
  }

  private resetSelection(): void {
    this.selectedAccounts = {};
  }
}
