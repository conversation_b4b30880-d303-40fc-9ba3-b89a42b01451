-- Create fbam_facebook_ad_accounts table to map users to their accessible ad accounts
CREATE TABLE fbam_facebook_ad_accounts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES fbam_facebook_users(id) ON DELETE CASCADE,
    ad_account_id TEXT NOT NULL, -- Facebook Ad Account ID (e.g., act_123456789)
    ad_account_name TEXT NOT NULL,
    account_status INTEGER DEFAULT 1, -- Facebook account status
    currency TEXT DEFAULT 'USD',
    timezone_name TEXT,
    business_id TEXT,
    business_name TEXT,
    permissions TEXT[], -- Specific permissions for this ad account
    is_accessible BOOLEAN DEFAULT TRUE, -- Whether the user can currently access this account
    last_synced TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique mapping per user-account combination
    UNIQUE(user_id, ad_account_id)
);

-- Create indexes for performance
CREATE INDEX idx_fbam_facebook_ad_accounts_user_id ON fbam_facebook_ad_accounts(user_id);
CREATE INDEX idx_fbam_facebook_ad_accounts_ad_account_id ON fbam_facebook_ad_accounts(ad_account_id);
CREATE INDEX idx_fbam_facebook_ad_accounts_accessible ON fbam_facebook_ad_accounts(is_accessible) WHERE is_accessible = true;

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_fbam_facebook_ad_accounts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_fbam_facebook_ad_accounts_updated_at
    BEFORE UPDATE ON fbam_facebook_ad_accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_fbam_facebook_ad_accounts_updated_at();

-- Add comments
COMMENT ON TABLE fbam_facebook_ad_accounts IS 'Mapping between Facebook users and their accessible ad accounts';
COMMENT ON COLUMN fbam_facebook_ad_accounts.ad_account_id IS 'Facebook Ad Account ID with act_ prefix';
COMMENT ON COLUMN fbam_facebook_ad_accounts.account_status IS 'Facebook account status (1=Active, 2=Disabled, etc.)';
COMMENT ON COLUMN fbam_facebook_ad_accounts.is_accessible IS 'Whether user currently has access to this ad account';
