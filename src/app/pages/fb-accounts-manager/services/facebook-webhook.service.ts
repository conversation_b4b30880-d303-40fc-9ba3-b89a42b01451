import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '../../../core';
import { FacebookTokenRequest, FacebookTokenResponse, FacebookAdAccount } from '../models';

@Injectable({
  providedIn: 'root',
})
export class FacebookWebhookService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService,
  ) {}

  /**
   * Get valid Facebook access token for a specific ad account
   * This endpoint will be used by N8N automations
   */
  getTokenForAccount(accountId: string): Observable<FacebookTokenResponse> {
    const url = `${this.configService.n8nBaseUrl}/webhook/fb-accounts-manager/tokens/get`;
    const payload: FacebookTokenRequest = { account_id: accountId };
    return this.http.post<FacebookTokenResponse>(url, payload);
  }

  /**
   * Get Facebook ad accounts for a specific user
   * GET /webhook/fb-accounts-manager/accounts?user_id={userId}
   */
  getAccountsForUser(userId: string): Observable<FacebookAdAccount[]> {
    const url = `${this.configService.n8nBaseUrl}/webhook/fb-accounts-manager/accounts`;
    const params = new HttpParams().set('user_id', userId);
    return this.http.get<FacebookAdAccount[]>(url, { params });
  }

  /**
   * Refresh Facebook ad accounts for a specific user
   * This will sync the user's accessible ad accounts from Facebook API
   */
  refreshAdAccounts(userId: string): Observable<{ success: boolean; accounts_synced: number }> {
    const url = `${this.configService.n8nBaseUrl}/webhook/fb-accounts-manager/accounts/refresh`;
    const payload = { user_id: userId };
    return this.http.post<{ success: boolean; accounts_synced: number }>(url, payload);
  }

  /**
   * Validate and refresh access token if needed
   */
  validateToken(userId: string): Observable<{ is_valid: boolean; expires_at?: string }> {
    const url = `${this.configService.n8nBaseUrl}/webhook/fb-accounts-manager/tokens/validate`;
    const payload = { user_id: userId };
    return this.http.post<{ is_valid: boolean; expires_at?: string }>(url, payload);
  }
}
