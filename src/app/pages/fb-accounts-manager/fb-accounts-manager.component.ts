import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { SkeletonModule } from 'primeng/skeleton';
import { ConfirmationService, MessageService } from 'primeng/api';

import { FacebookAppService } from './services';
import { FacebookWebhookService } from './services/facebook-webhook.service';
import {
  FacebookApp,
  FacebookLoginResponse,
  FacebookToken,
  FacebookUser,
  FacebookAccountSelectionData,
  FacebookAccountSelectionResult,
  FacebookAccountFromAPI,
} from './models';
import { AccountSelectionDialogComponent } from './components/account-selection-dialog.component';

@Component({
  selector: 'chm-fb-accounts-manager',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    CardModule,
    InputTextModule,
    PasswordModule,
    DialogModule,
    TableModule,
    TagModule,
    ToastModule,
    ConfirmDialogModule,
    SkeletonModule,
    AccountSelectionDialogComponent,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './fb-accounts-manager.component.html',
  styleUrls: ['./fb-accounts-manager.component.css'],
})
export class FbAccountsManagerComponent implements OnInit, OnDestroy {
  // Data
  facebookApps: FacebookApp[] = [];
  allTokens: FacebookToken[] = [];
  loading = false;

  // App Management
  showAppDialog = false;
  editingApp: FacebookApp | null = null;
  appForm: Partial<FacebookApp> = {};

  // Instructions Dialog
  showInstructionsDialog = false;
  currentHost = window.location.origin;

  // Facebook Login
  isLoggingInForApp: string | null = null;

  // Account Selection
  showAccountSelectionDialog = false;
  accountSelectionData: FacebookAccountSelectionData | null = null;
  currentUserId: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private facebookAppService: FacebookAppService,
    private facebookWebhookService: FacebookWebhookService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
  ) {}

  ngOnInit(): void {
    this.loadFacebookApps();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // App Management Methods
  loadFacebookApps(): void {
    this.loading = true;
    this.facebookAppService
      .getFacebookApps()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.facebookApps = response.data;
          // Load all tokens after apps are loaded
          this.loadAllTokens();
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading Facebook apps:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load Facebook apps',
          });
          this.loading = false;
        },
      });
  }

  openAppDialog(app?: FacebookApp): void {
    this.editingApp = app || null;
    this.appForm = app
      ? { ...app }
      : { name: '', app_id: '', app_secret: '', is_active: true };
    this.showAppDialog = true;
  }

  closeAppDialog(): void {
    this.showAppDialog = false;
    this.editingApp = null;
    this.appForm = {};
  }

  // Instructions Dialog Methods
  openInstructionsDialog(): void {
    this.showInstructionsDialog = true;
  }

  closeInstructionsDialog(): void {
    this.showInstructionsDialog = false;
  }

  closeInstructionsAndOpenApp(): void {
    this.closeInstructionsDialog();
    this.openAppDialog();
  }

  copyToClipboard(text: string): void {
    navigator.clipboard.writeText(text).then(() => {
      this.messageService.add({
        severity: 'success',
        summary: 'Copied',
        detail: 'Domain copied to clipboard',
      });
    }).catch(() => {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to copy to clipboard',
      });
    });
  }

  saveApp(): void {
    if (
      !this.appForm.name ||
      !this.appForm.app_id ||
      !this.appForm.app_secret
    ) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields',
      });
      return;
    }

    const operation = this.editingApp
      ? this.facebookAppService.updateFacebookApp(
          this.editingApp.id!,
          this.appForm,
        )
      : this.facebookAppService.createFacebookApp(
          this.appForm as Omit<FacebookApp, 'id'>,
        );

    operation.pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: `Facebook app ${this.editingApp ? 'updated' : 'created'} successfully`,
        });
        this.closeAppDialog();
        this.loadFacebookApps();
      },
      error: (error) => {
        console.error('Error saving Facebook app:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: `Failed to ${this.editingApp ? 'update' : 'create'} Facebook app`,
        });
      },
    });
  }

  deleteApp(app: FacebookApp): void {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete the Facebook app "${app.name}"?`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.facebookAppService
          .deleteFacebookApp(app.id!)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Facebook app deleted successfully',
              });
              this.loadFacebookApps();
            },
            error: (error) => {
              console.error('Error deleting Facebook app:', error);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete Facebook app',
              });
            },
          });
      },
    });
  }

  deleteToken(token: FacebookToken): void {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete this token?',
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.facebookAppService
          .deleteToken(token.id!)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'User removed successfully',
              });
              this.loadAllTokens();
            },
            error: (error) => {
              console.error('Error deleting token:', error);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete token',
              });
            },
          });
      },
    });
  }

  // Utility Methods
  isTokenExpired(token: FacebookToken): boolean {
    if (!token.expires_at) return false;
    return new Date(token.expires_at) < new Date();
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  getTimeUntilExpiration(expiresAt: string): string {
    const now = new Date();
    const expiration = new Date(expiresAt);
    const timeDiff = expiration.getTime() - now.getTime();

    if (timeDiff <= 0) {
      return 'Expired';
    }

    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor(
      (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
    );
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 30) {
      const months = Math.floor(days / 30);
      const remainingDays = days % 30;
      if (remainingDays > 0) {
        return `${months}M ${remainingDays}D`;
      }
      return `${months}M`;
    } else if (days > 0) {
      if (hours > 0) {
        return `${days}D ${hours}H`;
      }
      return `${days}D`;
    } else if (hours > 0) {
      if (minutes > 0) {
        return `${hours}H ${minutes}m`;
      }
      return `${hours}H`;
    } else {
      return `${minutes}m`;
    }
  }

  getExpirationSeverity(expiresAt: string): string {
    const now = new Date();
    const expiration = new Date(expiresAt);
    const timeDiff = expiration.getTime() - now.getTime();
    const daysLeft = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

    if (daysLeft <= 0) {
      return 'danger'; // Expired
    } else if (daysLeft <= 7) {
      return 'warning'; // Expires soon
    } else if (daysLeft <= 30) {
      return 'info'; // Expires in a month
    } else {
      return 'success'; // Good for a while
    }
  }

  // New methods for card-based design
  loadAllTokens(): void {
    // Get all tokens with a single query
    this.facebookAppService
      .getAllTokens()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tokens) => {
          this.allTokens = tokens;
        },
        error: (error) => {
          console.error('Error loading all tokens:', error);
        },
      });
  }

  getAppTokens(appId: string): FacebookToken[] {
    const app = this.facebookApps.find((a) => a.id === appId);
    if (!app) return [];
    return this.allTokens.filter((token) => token.app_id === app.app_id);
  }

  getTokenCount(appId: string): number {
    return this.getAppTokens(appId).length;
  }

  getExpiredTokenCount(appId: string): number {
    return this.getAppTokens(appId).filter(
      (token) => token.expires_at && this.isTokenExpired(token),
    ).length;
  }

  hasExpiredTokens(appId: string): boolean {
    return this.getExpiredTokenCount(appId) > 0;
  }

  async loginWithFacebookForApp(app: FacebookApp): Promise<void> {
    this.isLoggingInForApp = app.id!;

    try {
      // Initialize Facebook SDK for this app
      await this.facebookAppService.initializeFacebookSDK(app.app_id);

      // Login with Facebook
      const loginResponse: FacebookLoginResponse =
        await this.facebookAppService.loginWithFacebook();

      // Get user info
      const userInfo = await this.facebookAppService.getUserInfo(
        loginResponse.accessToken,
      );

      // Get user pages
      await this.facebookAppService.getUserPages(loginResponse.accessToken);
      const longLivedToken = await this.facebookAppService.extendAccessToken(
        loginResponse.accessToken,
        app.app_id,
        app.app_secret,
      );

      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 60);

      // Save the user with profile information
      const facebookUser: Omit<FacebookUser, 'id'> = {
        app_id: app.app_id,
        access_token: longLivedToken,
        facebook_user_id: userInfo.id,
        name: userInfo.name,
        email: userInfo.email,
        profile_picture_url: userInfo.picture?.data?.url,
        is_long_lived: true,
        permissions: [
          'ads_read',
          'business_management',
          'pages_show_list',
          'ads_management',
          'pages_read_engagement',
          'pages_manage_ads',
          'pages_read_user_content',
          'pages_manage_metadata',
          'pages_manage_posts',
        ],
        expires_at: expirationDate.toISOString(),
        last_login: new Date().toISOString(),
      };

      const savedUser = await this.facebookAppService.saveUser(facebookUser).toPromise();

      // Fetch ad accounts from N8N and show selection dialog
      if (savedUser?.id) {
        await this.fetchAndShowAccountSelection(savedUser.id, userInfo);
      } else {
        throw new Error('Failed to save user');
      }
    } catch (error) {
      console.error('Error during Facebook login:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to connect Facebook account',
      });
    } finally {
      this.isLoggingInForApp = null;
    }
  }

  /**
   * Fetch ad accounts from N8N and show account selection dialog
   */
  private async fetchAndShowAccountSelection(userId: string, userInfo: any): Promise<void> {
    try {
      // Call N8N webhook to get user's ad accounts
      const accounts = await this.facebookWebhookService.getAccountsForUser(userId).toPromise();

      if (!accounts || accounts.length === 0) {
        this.messageService.add({
          severity: 'info',
          summary: 'No Ad Accounts',
          detail: 'This Facebook user doesn\'t have access to any ad accounts.',
        });
        this.loadAllTokens();
        return;
      }

      // Convert to API format
      const apiAccounts: FacebookAccountFromAPI[] = accounts.map(acc => ({
        id: acc.ad_account_id,
        name: acc.ad_account_name,
        account_status: acc.account_status,
        currency: acc.currency,
        timezone_name: acc.timezone_name,
        business_id: acc.business_id,
        business_name: acc.business_name,
      }));

      // Check for conflicts
      const selectionData = await this.facebookAppService.checkAccountConflicts(apiAccounts).toPromise();

      // Add user info to selection data
      if (selectionData) {
        selectionData.user_info = {
          id: userId,
          name: userInfo.name,
          email: userInfo.email,
        };

        // Store current user ID and show dialog
        this.currentUserId = userId;
        this.accountSelectionData = selectionData;
        this.showAccountSelectionDialog = true;
      } else {
        throw new Error('Failed to check account conflicts');
      }

    } catch (error) {
      console.error('Error fetching ad accounts:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to fetch ad accounts. User was created but no accounts were added.',
      });
      this.loadAllTokens();
    }
  }

  /**
   * Handle account selection result
   */
  onAccountSelectionResult(result: FacebookAccountSelectionResult): void {
    this.showAccountSelectionDialog = false;

    if (!result.confirmed || !this.currentUserId || !this.accountSelectionData) {
      this.messageService.add({
        severity: 'info',
        summary: 'Cancelled',
        detail: 'Account selection was cancelled.',
      });
      this.loadAllTokens();
      return;
    }

    if (result.selected_accounts.length === 0) {
      this.messageService.add({
        severity: 'info',
        summary: 'No Accounts Selected',
        detail: 'No ad accounts were selected.',
      });
      this.loadAllTokens();
      return;
    }

    // Save selected accounts
    this.facebookAppService.saveSelectedAccounts(
      this.currentUserId,
      result.selected_accounts,
      this.accountSelectionData.available_accounts
    ).subscribe({
      next: (savedAccounts) => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: `Successfully added ${savedAccounts.length} ad account(s).`,
        });
        this.loadAllTokens();
      },
      error: (error) => {
        console.error('Error saving selected accounts:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to save selected accounts.',
        });
        this.loadAllTokens();
      }
    });

    // Reset state
    this.currentUserId = null;
    this.accountSelectionData = null;
  }
}
