export interface FacebookApp {
  id?: string;
  name: string;
  app_id: string;
  app_secret: string;
  client_id?: string; // Same as app_id, for clarity
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface FacebookUser {
  id?: string;
  app_id: string;
  access_token?: string;
  facebook_user_id: string;
  name: string;
  email?: string;
  profile_picture_url?: string;
  is_long_lived: boolean;
  permissions?: string[];
  expires_at?: string;
  last_login?: string;
  created_at?: string;
  updated_at?: string;
}

// Keep FacebookToken as alias for backward compatibility
export type FacebookToken = FacebookUser;

export interface FacebookLoginResponse {
  accessToken: string;
  userID: string;
  expiresIn: number;
  signedRequest: string;
  graphDomain: string;
  data_access_expiration_time: number;
}

export interface FacebookUserInfo {
  id: string;
  name: string;
  email?: string;
  picture?: {
    data: {
      url: string;
    };
  };
}

export interface FacebookPage {
  id: string;
  name: string;
  access_token: string;
  category: string;
  tasks?: string[];
}

export interface FacebookAdAccount {
  id?: string;
  user_id: string;
  ad_account_id: string;
  ad_account_name: string;
  account_status: number;
  currency: string;
  timezone_name?: string;
  business_id?: string;
  business_name?: string;
  permissions?: string[];
  is_accessible: boolean;
  last_synced?: string;
  created_at?: string;
  updated_at?: string;
}

export interface FacebookUserWithAdAccounts extends FacebookUser {
  ad_accounts?: FacebookAdAccount[];
}

export interface FacebookAppFilters {
  is_active?: boolean;
  search?: string;
}

export interface FacebookAppResponse {
  data: FacebookApp[];
  total_count: number;
}

export interface FacebookAppConfig {
  app: FacebookApp;
  users: FacebookUser[];
  user_info?: FacebookUserInfo;
}

// N8N Integration types
export interface FacebookTokenRequest {
  account_id: string;
}

export interface FacebookTokenResponse {
  access_token: string;
  user_id: string;
  user_name: string;
  app_id: string;
  expires_at?: string;
  is_valid: boolean;
}

// Account selection types
export interface FacebookAccountFromAPI {
  id: string;
  name: string;
  account_status: number;
  currency: string;
  timezone_name?: string;
  business_id?: string;
  business_name?: string;
}

export interface FacebookAccountConflict {
  account: FacebookAccountFromAPI;
  existing_user: {
    id: string;
    name: string;
    email?: string;
  };
}

export interface FacebookAccountSelectionData {
  available_accounts: FacebookAccountFromAPI[];
  conflicted_accounts: FacebookAccountConflict[];
  user_info: {
    id: string;
    name: string;
    email?: string;
  };
}

export interface FacebookAccountSelectionResult {
  selected_accounts: string[]; // Array of ad_account_ids
  confirmed: boolean;
}
